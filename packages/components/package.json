{"name": "@zeta-gds/components", "version": "1.11.0", "description": "A Vue 3 Component Library. Fairly Complete, Theme Customizable, Uses TypeScript, Fast.", "main": "lib/index.js", "module": "lib/index.js", "types": "lib/index.d.ts", "unpkg": "lib/index.js", "jsdelivr": "lib/index.js", "packageManager": "pnpm@8.6.10", "publishConfig": {"access": "public", "registry": "https://verdaccio.internal.olympus-world.zetaapps.in"}, "web-types": "./web-types.json", "scripts": {"build": "pnpm run build:package", "postbuild": "pnpm run gen-web-types", "pack-it": "pnpm pack", "build:package": "pnpm run gen-version && pnpm run clean && pnpm run gen-volar-dts && tsc -b --force tsconfig.esm.json && node scripts/post-build && rimraf lib/*.tsbuildinfo", "clean": "rimraf lib fonts node_modules/@zeta-gds/components", "release:package": "pnpm run test && pnpm run build:package && pnpm publish --no-git-checks", "lint": "pnpm run lint:code && pnpm run lint:type", "lint:type": "pnpm run lint:src-type", "lint:code": "eslint \"{src,build,scripts}/**/*.{ts,tsx,js,vue,md}\"", "lint:fix": "eslint --fix \"{src,build,scripts}/**/*.{ts,tsx,js,vue,md}\"", "lint:src-type": "tsc -b --force tsconfig.esm.json", "format": "pnpm run format:code && pnpm run format:md && pnpm run lint:fix", "format:code": "prettier --write \"(src)/**/*.(vue|js)\"", "format:md": "prettier --write --parser markdown --prose-wrap never \"(src)/**/*.md\"", "test": "NODE_ENV=test NODE_OPTIONS=\"--max_old_space_size=8192 --experimental-vm-modules\" jest --runInBand --no-cache --collectCoverage=false", "test:update": "NODE_ENV=test jest -u --collectCoverage=false", "test:cov": "NODE_ENV=test jest --coverage", "test:update-snapshot": "NODE_ENV=test jest  --updateSnapshot", "test:watch": "NODE_ENV=test JEST_MODE=watch jest ---watch --verbose --coverage", "test:umd": "jest --collectCoverage=false --testMatch=\"<rootDir>/umd-test/index.spec.js\"", "gen-version": "node scripts/gen-version", "gen-web-types": "esbuild scripts/gen-web-types.js --bundle --platform=node --tsconfig=tsconfig.esbuild.json | node", "gen-volar-dts": "esbuild scripts/gen-component-declaration.js --bundle --platform=node --tsconfig=tsconfig.esbuild.json | node"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Sai Varudu", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "files": ["lib", "volar.d.ts", "web-types.json", "README.md"], "dependencies": {"@emotion/hash": "~0.8.0", "@juggle/resize-observer": "^3.4.0", "@vueuse/core": "^10.7.1", "async-validator": "^4.2.5", "axios": "^1.8.4", "csstype": "~3.0.11", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "highlight.js": "^11.9.0", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "seemly": "^0.3.8", "treemate": "^0.3.11", "vue-content-loader": "^2.0.1", "vue-json-pretty": "^2.3.0"}, "devDependencies": {"@babel/core": "^7.23.7", "@babel/eslint-parser": "^7.23.3", "@babel/generator": "^7.23.6", "@babel/parser": "^7.23.6", "@babel/preset-env": "^7.23.8", "@babel/traverse": "^7.23.7", "@babel/types": "^7.23.7", "@faker-js/faker": "^8.4.0", "@happy-dom/jest-environment": "^14.0.0", "@types/estree": "^1.0.5", "@types/jest": "^29.5.12", "@types/jsdom": "21.1.7", "@types/katex": "^0.16.7", "@types/lodash": "^4.17.18", "@types/lodash-es": "^4.17.12", "@types/markdown-it": "^14.1.2", "@types/node": "^20.11.0", "@types/web-bluetooth": "^0.0.20", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vicons/fluent": "^0.12.0", "@vicons/ionicons4": "^0.12.0", "@vicons/ionicons5": "^0.12.0", "@vue/compiler-sfc": "~3.3", "@vue/eslint-config-standard": "^8.0.1", "@vue/eslint-config-typescript": "^11.0.3", "@vue/server-renderer": "~3.3", "@vue/test-utils": "^2.4.5", "@zeta/icons": "^1.0.0", "autoprefixer": "^10.4.16", "babel-jest": "^29.7.0", "cssnano": "^6.0.3", "deepmerge": "^4.3.1", "esbuild": "0.18.12", "eslint": "^8.56.0", "eslint-config-prettier": "^8.10.0", "eslint-config-standard": "^17.1.0", "eslint-config-standard-with-typescript": "^35.0.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-markdown": "^3.0.1", "eslint-plugin-n": "^15.7.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^9.20.0", "express": "^4.19.2", "express-rate-limit": "^5.3.0", "fast-glob": "^3.3.2", "fs-extra": "^11.2.0", "grapheme-splitter": "^1.0.4", "happy-dom": "^14.0.0", "helmet": "^4.6.0", "jest": "^29.7.0", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "^29.7.0", "jest-html-reporters": "^3.1.7", "katex": "^0.16.10", "lyla": "~1.1.1", "marked": "^5.1.2", "md-editor-v3": "4.21.3", "rimraf": "^5.0.5", "superagent": "^8.1.2", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "5.1.3", "vfonts": "^0.1.0", "vue": "~3.3", "vue-router": "^4.2.5", "vue-tsc": "^1.8.27"}, "peerDependencies": {"vue": "~3.3"}, "sideEffects": false, "homepage": "https://www.gds.zeta.tech", "repository": {"type": "git", "url": "https://bitbucket.org/zetaengg/galaxy-design-system"}, "license": "MIT", "keywords": ["@zeta-gds/components", "component library", "ui framework", "ui", "vue"]}